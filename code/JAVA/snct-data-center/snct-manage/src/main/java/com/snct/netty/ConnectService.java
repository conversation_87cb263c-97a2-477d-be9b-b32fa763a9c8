package com.snct.netty;

import com.snct.common.utils.spring.SpringUtils;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.PackageTypeEnum;
import com.snct.dctcore.commoncore.utils.JsonUtil;
import com.snct.dctcore.commoncore.utils.NumUtils;
import com.snct.service.LinkStatusService;
import com.snct.service.StoreService;
import com.snct.system.service.DeviceDataMysqlService;
import com.snct.system.service.DynamicTableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName: ConnectService
 * @Description: udp连接服务
 * @author: wzewei
 * @date: 2025-09-03 10:30
 */
@Service
public class ConnectService {

    private Logger logger = LoggerFactory.getLogger(ConnectService.class);
    /**
     * 岸上UPD监听 端口
     */
    @Value("${data-center.udpPort}")
    private Integer udpPort;

    @Resource
    private DataHandleService dataHandleService;

    @Autowired
    private SendService sendService;

    @Autowired
    private DeviceDataMysqlService deviceDataMysqlService;

    @Autowired
    private DynamicTableService dynamicTableService;

    @Autowired
    private LinkStatusService linkStatusService;

    @Autowired
    private StoreService storeService;

    /**
     * 创建UDP接收服务
     */
    public void initUdpServer() {
        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            new UdpServer(udpPort);
        });
    }


    /**
     * 处理接收到的消息
     *
     * @param transferPackage
     */
    public void handleMessage(TransferPackage transferPackage) {
        try {
            if (transferPackage == null) {
                return;
            }
            // 保持连接的信息,不做处理
            if (PackageTypeEnum.CONNECT_KEEP.getValue().equals(transferPackage.getPackageType())) {
                logger.info("接到数据,--保持连接---{}", JsonUtil.obj2String(transferPackage));
                return;
            }

            // 处理传感器数据
            if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
                if (dataHandleService.removeNonExistData(transferPackage)) {
                    return;
                }

                // 北斗链路Bc1cfr,sn   网络链路Bc2cfr,sn

                String[] sns = transferPackage.getSn().split(",");

                String linkType = sns[0];
                System.out.println("链路类型：" + linkType);

                String beidouNm = sns[1];//北斗的编号
                if (beidouNm.equalsIgnoreCase("1111111")) {
                    return;
                }

                // 更新链路状态
                if ("Bc1cfr".equals(linkType)) {
                    linkStatusService.updateBeidouStatus(beidouNm);
                } else if ("Bc2cfr".equals(linkType)) {
                    linkStatusService.updateNetworkStatus(beidouNm);
                    KafkaMessage kafkaMessage = new KafkaMessage();
                    kafkaMessage.setSn(beidouNm);
                    kafkaMessage.setType(transferPackage.getDeviceType());
                    kafkaMessage.setCode(transferPackage.getDeviceCode());
                    kafkaMessage.setMsg(transferPackage.getMessage());
                    kafkaMessage.setCost(10);
                    kafkaMessage.setInitialTime(transferPackage.getTime());
                    storeService.save2Hbase(kafkaMessage);
                }

                transferPackage.setSn(beidouNm);//真正的SN
                //if (beidouNm.equalsIgnoreCase("8888888")) {
                //    //第三方无用数据
                //    return;
                //} else {

                //如果存在则往下走
                dataHandleService.receiveMessage(transferPackage);
            }

        } catch (Exception e) {
            logger.error("处理数据失败，--{}", e);
        }
    }

    /**
     * 解析传输的UDP包
     *
     * @param msg
     * @return
     */
    public TransferPackage analysisPackage(byte[] msg) throws Exception {
        int length = msg.length;
        if (length < 28) {
            return null;
        }

        TransferPackage transferPackage = new TransferPackage();

        int offset = 0;

        // 船只序列号
        transferPackage.setSn(new String(NumUtils.splitBytes(msg, offset, 14)));
        offset += 14;

        // 设备类型
        transferPackage.setPackageType((int) NumUtils.splitByte(msg, offset));
        offset++;

        // 统一编码
        transferPackage.setCommandNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        //时间戳
        transferPackage.setTime(NumUtils.bytesToLong(NumUtils.splitBytes(msg, offset, 8)));
        offset += 8;

        // 是否补发
        transferPackage.setIsRepair((int) NumUtils.splitByte(msg, offset));
        offset++;

        if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
            // 设备类型
            transferPackage.setDeviceType((int) NumUtils.splitByte(msg, offset));
            offset++;
        }

        // 设备编码
        transferPackage.setDeviceCode(new String(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        if (PackageTypeEnum.SNAPSHOT_DATA.getValue().equals(transferPackage.getPackageType())) {
            // 拆包总数
            transferPackage.setUnpackingTotal(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;

            // 拆包编号
            transferPackage.setUnpackingNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;
        }

        // 消息主体
        transferPackage.setMessage(new String(NumUtils.splitBytes(msg, offset, length - offset)));

        return transferPackage;
    }

    /**
     * 根据设备类型获取设备类型别名
     *
     * @param deviceType 设备类型
     * @return 设备类型别名
     */
    private String getDeviceTypeAlias(Integer deviceType) {
        if (deviceType == null) {
            return null;
        }

        try {
            com.snct.dctcore.commoncore.enums.DeviceTypeEnum deviceTypeEnum =
                    com.snct.dctcore.commoncore.enums.DeviceTypeEnum.getByValue(deviceType);
            return deviceTypeEnum != null ? deviceTypeEnum.getAlias().toLowerCase() : null;
        } catch (Exception e) {
            logger.warn("获取设备类型别名失败: {}", e.getMessage());
            return null;
        }
    }

}