package com.snct.service;

import com.snct.common.core.redis.RedisCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName: LinkStatusService
 * @Description: 双链路状态管理服务
 * @author: wzewei
 * @date: 2025-09-04 10:30
 */
@Service
public class LinkStatusService {
    
    private static final Logger logger = LoggerFactory.getLogger(LinkStatusService.class);
    
    @Autowired
    private RedisCache redisCache;
    
    // 链路状态过期时间（秒）
    private static final int LINK_EXPIRE_SECONDS = 60;
    
    // Redis Key前缀
    private static final String BEIDOU_STATUS_PREFIX = "BEIDOU_LINK:";
    private static final String NETWORK_STATUS_PREFIX = "NETWORK_LINK:";
    
    /**
     * 更新北斗链路状态
     * 
     * @param sn 船只序列号
     */
    public void updateBeidouStatus(String sn) {
        String key = BEIDOU_STATUS_PREFIX + sn;
        redisCache.setCacheObject(key, System.currentTimeMillis(), LINK_EXPIRE_SECONDS, TimeUnit.SECONDS);
        logger.debug("更新北斗链路状态: sn={}", sn);
    }
    
    /**
     * 更新网络链路状态
     * 
     * @param sn 船只序列号
     */
    public void updateNetworkStatus(String sn) {
        String key = NETWORK_STATUS_PREFIX + sn;
        redisCache.setCacheObject(key, System.currentTimeMillis(), LINK_EXPIRE_SECONDS, TimeUnit.SECONDS);
        logger.debug("更新网络链路状态: sn={}", sn);
    }
    
    /**
     * 检查北斗链路是否正常
     * 
     * @param sn 船只序列号
     * @return true-正常，false-断开
     */
    public boolean isBeidouActive(String sn) {
        return redisCache.hasKey(BEIDOU_STATUS_PREFIX + sn);
    }
    
    /**
     * 检查网络链路是否正常
     * 
     * @param sn 船只序列号
     * @return true-正常，false-断开
     */
    public boolean isNetworkActive(String sn) {
        return redisCache.hasKey(NETWORK_STATUS_PREFIX + sn);
    }
    
    /**
     * 检查是否有任一链路正常
     * 
     * @param sn 船只序列号
     * @return true-至少有一条链路正常，false-所有链路均断开
     */
    public boolean hasActiveLink(String sn) {
        return isBeidouActive(sn) || isNetworkActive(sn);
    }
    
    /**
     * 获取链路状态信息（用于日志记录）
     * 
     * @param sn 船只序列号
     * @return 状态描述字符串
     */
    public String getLinkStatusInfo(String sn) {
        boolean beidouActive = isBeidouActive(sn);
        boolean networkActive = isNetworkActive(sn);
        
        if (beidouActive && networkActive) {
            return "双链路正常";
        } else if (networkActive) {
            return "仅网络链路正常";
        } else if (beidouActive) {
            return "仅北斗链路正常";
        } else {
            return "所有链路断开";
        }
    }
}
