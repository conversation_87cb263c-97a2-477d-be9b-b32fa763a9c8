package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.PduHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;

import com.snct.system.service.DeviceDataQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: PduService
 * @Description: Pdu数据服务
 * @author: wzewei
 * @date: 2025-08-15 09:21
 */
@Service
@Transactional(readOnly = true)
public class PduService {

    private final Logger logger = LoggerFactory.getLogger(PduService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private DeviceDataQueryService deviceDataQueryService;


    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<PduHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.PDU.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new PduHbaseVo(), tableName, rowList);
    }

    public PduHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.PDU.getAlias(), deviceCode, 100);
        return hBaseDaoUtil.getLatestRow(new PduHbaseVo(), tableName);
    }

    /**
     * @param sn         设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串
     * @param interval   时间间隔
     * @param startTime  开始时间戳
     * @param endTime    结束时间戳
     * @return PDU数据列表
     */
    public List<PduHbaseVo> queryByTimeFromMysql(String sn, String deviceCode, String deviceType,
                                                 Integer interval, Long startTime, Long endTime) {
        try {
            logger.debug("从MySQL查询PDU数据: sn={}, deviceCode={}, deviceType={}, interval={}, startTime={}, endTime={}",
                    sn, deviceCode, deviceType, interval, startTime, endTime);

            // 北斗数据本身就有时间间隔，直接按时间范围查询所有数据
            List<Map<String, Object>> dataList = deviceDataQueryService.queryDeviceDataByTimeRange(sn, deviceCode, deviceType, startTime, endTime);

            if (dataList == null || dataList.isEmpty()) {
                logger.warn("MySQL查询PDU数据为空: sn={}, deviceCode={}", sn, deviceCode);
                return new ArrayList<>();
            }

            // 将MySQL查询结果转换为PduHbaseVo对象
            List<PduHbaseVo> pduDataList = dataList.stream()
                    .map(this::convertMysqlDataToPduHbaseVo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            logger.debug("MySQL查询PDU数据成功，返回{}条记录", pduDataList.size());
            return pduDataList;

        } catch (Exception e) {
            logger.error("从MySQL查询PDU数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从MySQL数据库获取PDU设备的最新一条数据
     *
     * @param sn         设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串（如"pdu"）
     * @return PDU最新数据
     */
    public PduHbaseVo getLatestDataFromMysql(String sn, String deviceCode, String deviceType) {
        try {
            logger.debug("从MySQL获取PDU最新数据: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType);

            Map<String, Object> latestData = deviceDataQueryService.getLatestDeviceDataCompatible(sn, deviceCode,
                    deviceType);

            if (latestData == null) {
                logger.warn("MySQL中未找到PDU最新数据: sn={}, deviceCode={}", sn, deviceCode);
                return null;
            }

            PduHbaseVo pduData = convertMysqlDataToPduHbaseVo(latestData);

            if (pduData != null) {
                logger.debug("MySQL获取PDU最新数据成功: sn={}, deviceCode={}, time={}",
                        sn, deviceCode, pduData.getInitialBjTime());
            }

            return pduData;

        } catch (Exception e) {
            logger.error("从MySQL获取PDU最新数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return null;
        }
    }

    /**
     * 将MySQL查询结果转换为PduHbaseVo对象
     *
     * @param mysqlData MySQL查询结果
     * @return PduHbaseVo对象
     */
    private PduHbaseVo convertMysqlDataToPduHbaseVo(Map<String, Object> mysqlData) {
        if (mysqlData == null) {
            return null;
        }

        try {
            PduHbaseVo pduVo = new PduHbaseVo();

            // 设置基础字段
            pduVo.setId(getString(mysqlData, "id"));
            pduVo.setInitialTime(getString(mysqlData, "initialTime"));
            pduVo.setInitialBjTime(getString(mysqlData, "initialBjTime"));
            //pduVo.setUtcTime(getString(mysqlData, "utcTime"));

            // 设置PDU特有字段
            pduVo.setManage(getString(mysqlData, "manage"));
            pduVo.setElectric(getString(mysqlData, "electric"));
            pduVo.setVoltage(getString(mysqlData, "voltage"));
            pduVo.setYesPower(getString(mysqlData, "yesPower"));
            pduVo.setNoPower(getString(mysqlData, "noPower"));
            pduVo.setSeePower(getString(mysqlData, "seePower"));
            pduVo.setPowerParam(getString(mysqlData, "powerParam"));

            // 设置8个通道的数据
            for (int i = 1; i <= 8; i++) {
                pduVo.setChannelData(i, getString(mysqlData, "out" + i + "Electric"), getString(mysqlData,
                        "out" + i + "Power"), getString(mysqlData, "out" + i + "Status"));
            }

            return pduVo;

        } catch (Exception e) {
            logger.error("转换MySQL数据到PduHbaseVo失败", e);
            return null;
        }
    }

    /**
     * 安全获取字符串值
     *
     * @param map 数据映射
     * @param key 键名
     * @return 字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

}
