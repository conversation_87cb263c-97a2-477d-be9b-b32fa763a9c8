package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.ModemHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;

import com.snct.system.service.DeviceDataQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: ModemService
 * @Description: 卫星猫数据服务
 * @author: wzewei
 * @date: 2025-08-15 09:21
 */
@Service
@Transactional(readOnly = true)
public class ModemService {

    private final Logger logger = LoggerFactory.getLogger(ModemService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private DeviceDataQueryService deviceDataQueryService;


    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ModemHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.MODEM.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new ModemHbaseVo(), tableName, rowList);
    }

    public ModemHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.MODEM.getAlias(), deviceCode,100);
        return hBaseDaoUtil.getLatestRow(new ModemHbaseVo(), tableName);
    }

    /**
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串
     * @param interval 时间间隔
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return Modem数据列表
     */
    public List<ModemHbaseVo> queryByTimeFromMysql(String sn, String deviceCode, String deviceType,
                                                   Integer interval, Long startTime, Long endTime) {
        try {
            logger.debug("从MySQL查询Modem数据: sn={}, deviceCode={}, deviceType={}, startTime={}, endTime={}",
                        sn, deviceCode, deviceType, startTime, endTime);

            // 北斗数据本身就有时间间隔，直接按时间范围查询所有数据
            List<Map<String, Object>> dataList = deviceDataQueryService.queryDeviceDataByTimeRange(sn, deviceCode, deviceType, startTime, endTime);

            if (dataList == null || dataList.isEmpty()) {
                logger.warn("MySQL查询Modem数据为空: sn={}, deviceCode={}", sn, deviceCode);
                return new ArrayList<>();
            }

            // 将MySQL查询结果转换为ModemHbaseVo对象
            List<ModemHbaseVo> modemDataList = dataList.stream()
                .map(this::convertMysqlDataToModemHbaseVo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            logger.debug("MySQL查询Modem数据成功，返回{}条记录", modemDataList.size());
            return modemDataList;

        } catch (Exception e) {
            logger.error("从MySQL查询Modem数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从MySQL数据库获取Modem设备的最新一条数据
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串（如"modem"）
     * @return Modem最新数据
     */
    public ModemHbaseVo getLatestDataFromMysql(String sn, String deviceCode, String deviceType) {
        try {
            logger.debug("从MySQL获取Modem最新数据: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType);

            Map<String, Object> latestData = deviceDataQueryService.getLatestDeviceDataCompatible(sn, deviceCode, deviceType);

            if (latestData == null) {
                logger.warn("MySQL中未找到Modem最新数据: sn={}, deviceCode={}", sn, deviceCode);
                return null;
            }

            ModemHbaseVo modemData = convertMysqlDataToModemHbaseVo(latestData);

            if (modemData != null) {
                logger.debug("MySQL获取Modem最新数据成功: sn={}, deviceCode={}, time={}",
                           sn, deviceCode, modemData.getInitialBjTime());
            }

            return modemData;

        } catch (Exception e) {
            logger.error("从MySQL获取Modem最新数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return null;
        }
    }

    /**
     * 将MySQL查询结果转换为ModemHbaseVo对象
     *
     * @param mysqlData MySQL查询结果
     * @return ModemHbaseVo对象
     */
    private ModemHbaseVo convertMysqlDataToModemHbaseVo(Map<String, Object> mysqlData) {
        if (mysqlData == null) {
            return null;
        }

        try {
            ModemHbaseVo modemVo = new ModemHbaseVo();

            // 设置基础字段
            modemVo.setId(getString(mysqlData, "id"));
            //modemVo.setInitialTime(getString(mysqlData, "initialTime"));
            modemVo.setInitialBjTime(getString(mysqlData, "initialBjTime"));
            //modemVo.setUtcTime(getString(mysqlData, "utcTime"));

            // 设置Modem特有字段
            modemVo.setSignal(getString(mysqlData, "signal"));
            modemVo.setSpeed(getString(mysqlData, "speed"));
            modemVo.setSendPower(getString(mysqlData, "sendPower"));
            modemVo.setFlag(getString(mysqlData, "flag"));

            return modemVo;

        } catch (Exception e) {
            logger.error("转换MySQL数据到ModemHbaseVo失败", e);
            return null;
        }
    }

    /**
     * 安全获取字符串值
     *
     * @param map 数据映射
     * @param key 键名
     * @return 字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

}
