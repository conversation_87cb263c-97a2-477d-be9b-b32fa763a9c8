package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.AmplifierHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.snct.system.service.DeviceDataQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: AmplifierService
 * @Description: Amplifier数据服务
 * @author: wzewei
 * @date: 2025-08-15 09:21
 */
@Service
@Transactional(readOnly = true)
public class AmplifierService {

    private final Logger logger = LoggerFactory.getLogger(AmplifierService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private DeviceDataQueryService deviceDataQueryService;


    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<AmplifierHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AMPLIFIER.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new AmplifierHbaseVo(), tableName, rowList);
    }

    public AmplifierHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AMPLIFIER.getAlias(), deviceCode,100);
        return hBaseDaoUtil.getLatestRow(new AmplifierHbaseVo(), tableName);
    }

    /**
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串
     * @param interval 时间间隔
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return Amplifier数据列表
     */
    public List<AmplifierHbaseVo> queryByTimeFromMysql(String sn, String deviceCode, String deviceType,
                                                       Integer interval, Long startTime, Long endTime) {
        try {
            logger.debug("从MySQL查询Amplifier数据: sn={}, deviceCode={}, deviceType={}, startTime={}, endTime={}",
                        sn, deviceCode, deviceType, startTime, endTime);

            // 北斗数据本身就有时间间隔，直接按时间范围查询所有数据
            List<Map<String, Object>> dataList = deviceDataQueryService.queryDeviceDataByTimeRange(sn, deviceCode, deviceType, startTime, endTime);

            if (dataList == null || dataList.isEmpty()) {
                logger.warn("MySQL查询Amplifier数据为空: sn={}, deviceCode={}", sn, deviceCode);
                return new ArrayList<>();
            }

            // 将MySQL查询结果转换为AmplifierHbaseVo对象
            List<AmplifierHbaseVo> amplifierDataList = dataList.stream()
                .map(this::convertMysqlDataToAmplifierHbaseVo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            logger.debug("MySQL查询Amplifier数据成功，返回{}条记录", amplifierDataList.size());
            return amplifierDataList;

        } catch (Exception e) {
            logger.error("从MySQL查询Amplifier数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从MySQL数据库获取Amplifier设备的最新一条数据
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串（如"amplifier"）
     * @return Amplifier最新数据
     */
    public AmplifierHbaseVo getLatestDataFromMysql(String sn, String deviceCode, String deviceType) {
        try {
            logger.debug("从MySQL获取Amplifier最新数据: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType);

            Map<String, Object> latestData = deviceDataQueryService.getLatestDeviceDataCompatible(sn, deviceCode, deviceType);

            if (latestData == null) {
                logger.warn("MySQL中未找到Amplifier最新数据: sn={}, deviceCode={}", sn, deviceCode);
                return null;
            }

            AmplifierHbaseVo amplifierData = convertMysqlDataToAmplifierHbaseVo(latestData);

            if (amplifierData != null) {
                logger.debug("MySQL获取Amplifier最新数据成功: sn={}, deviceCode={}, time={}",
                           sn, deviceCode, amplifierData.getInitialBjTime());
            }

            return amplifierData;

        } catch (Exception e) {
            logger.error("从MySQL获取Amplifier最新数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return null;
        }
    }

    /**
     * 将MySQL查询结果转换为AmplifierHbaseVo对象
     *
     * @param mysqlData MySQL查询结果
     * @return AmplifierHbaseVo对象
     */
    private AmplifierHbaseVo convertMysqlDataToAmplifierHbaseVo(Map<String, Object> mysqlData) {
        if (mysqlData == null) {
            return null;
        }

        try {
            AmplifierHbaseVo amplifierVo = new AmplifierHbaseVo();

            // 设置基础字段
            amplifierVo.setId(getString(mysqlData, "id"));
            amplifierVo.setInitialTime(getString(mysqlData, "initialTime"));
            amplifierVo.setInitialBjTime(getString(mysqlData, "initialBjTime"));
            //amplifierVo.setUtcTime(getString(mysqlData, "utcTime"));

            // 设置Amplifier特有字段
            amplifierVo.setDecay(getString(mysqlData, "decay"));
            amplifierVo.setTemp(getString(mysqlData, "temp"));
            amplifierVo.setOutPower(getString(mysqlData, "outPower"));
            amplifierVo.setBucStatus(getString(mysqlData, "status"));

            return amplifierVo;

        } catch (Exception e) {
            logger.error("转换MySQL数据到AmplifierHbaseVo失败", e);
            return null;
        }
    }

    /**
     * 安全获取字符串值
     *
     * @param map 数据映射
     * @param key 键名
     * @return 字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

}
